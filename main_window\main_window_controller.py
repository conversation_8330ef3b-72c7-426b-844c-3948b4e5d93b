import os
import platform
from typing import Union

from PyQt6.QtCore import QObject
from PyQt6.QtGui import QShortcut, QKeySequence
from PyQt6.QtCore import Qt

from main_window.main_window_view import MainWindowView
from services import (
    <PERSON>ceManager,
    CaptureOrchestrator,
    StorageService,
    SequenceCounter,
)
from models import CaptureMetadata
from core.config import get_config
from core.logging_config import get_logger
from core.container import get_container

logger = get_logger(__name__)





class MainWindowController(QObject):
    """
    The main window controller of the application.

    This class is responsible for handling application logic, connecting signals
    and slots, and managing the interaction between the view and the models.
    """

    def __init__(self, project_root: str) -> None:
        super().__init__()
        self.project_root = project_root
        self._config = get_config()
        self._container = get_container()

        # -----------------------------
        # Services Initialization via DI
        # -----------------------------
        logger.info("Initializing services via dependency injection...")

        self.device_manager = self._container.get(DeviceManager)
        self.device_manager.discover_cameras()

        self.storage_service = self._container.get(StorageService)
        self.sequence_counter = self._container.get(SequenceCounter)

        # -----------------------------
        # View Initialization
        # -----------------------------
        self.view = MainWindowView(
            project_root, self.device_manager, default_storage_path=storage_root
        )
        self.capture_orchestrator = CaptureOrchestrator(self.view.preview_grid)

        # -----------------------------
        # Initial State Setup
        # -----------------------------
        self._set_initial_metadata()

        # -----------------------------
        # Signal and Slot Connections
        # -----------------------------
        self._connect_signals()

        logger.info("Main window controller initialization completed")

    def _set_initial_metadata(self):
        """Set the initial metadata in the view."""
        initial_metadata: CaptureMetadata = self.view.metadata_panel.get_metadata()
        initial_metadata.sequence_number = self.sequence_counter.get_current()
        self.view.metadata_panel.set_metadata(initial_metadata)

    def _connect_signals(self):
        """Connect signals from the view to the controller's slots."""
        self.view.metadata_panel.capture_button.clicked.connect(self.on_capture)
        self.view.metadata_panel.sequence_number_edit.textChanged.connect(
            self.on_sequence_changed
        )
        self.view.settings_panel.path_edit.textChanged.connect(self.on_storage_path_changed)

        QShortcut(QKeySequence(Qt.Key.Key_Space), self.view, self.on_capture)

    def show(self):
        """Show the main window."""
        self.view.show()

    # ------------------------------------------------------------------
    # Slot Methods
    # ------------------------------------------------------------------

    def on_capture(self):
        """Handle the capture button click."""
        try:
            metadata = self.view.metadata_panel.get_metadata()
            settings = self.view.settings_panel.get_settings()

            logger.info(f"Starting capture with metadata: {metadata.to_dict()}")
            self.view.log_panel.add_log_message(f"Capturing with metadata: {metadata.to_dict()}")

            frames = self.capture_orchestrator.capture_all_frames()
            if frames:
                session_dir = self.storage_service.save(frames, metadata, settings)
                success_msg = f"Saved {len(frames)} frames to: {session_dir}"
                logger.info(success_msg)
                self.view.log_panel.add_log_message(success_msg)

                if not self.view.metadata_panel.lock_checkbox.isChecked():
                    self.sequence_counter.increment()
                    metadata.sequence_number = self.sequence_counter.get_current()
                    self.view.metadata_panel.set_metadata(metadata)
            else:
                error_msg = "Capture failed. No frames received."
                logger.warning(error_msg)
                self.view.log_panel.add_log_message(error_msg)

        except Exception as e:
            error_msg = f"Error during capture: {e}"
            logger.error(error_msg, exc_info=True)
            self.view.log_panel.add_log_message(error_msg)

    def on_sequence_changed(self, text: str):
        """Handle the sequence number change."""
        try:
            new_seq = int(text)
            self.sequence_counter.set_current(new_seq)
        except ValueError:
            # Ignore non-numeric input
            pass

    def on_storage_path_changed(self, path: str):
        """Handle the storage path change."""
        self.storage_service.set_root_dir(path)
