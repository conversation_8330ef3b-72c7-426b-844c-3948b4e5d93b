"""
Metadata panel widget for capture configuration.

This module provides a widget for configuring capture metadata
including lighting conditions, background ID, and sequence numbers.
"""

from typing import Optional

from PyQt6.QtWidgets import QPushButton, QLineEdit, QComboBox, QCheckBox
from PyQt6.QtCore import pyqtSignal

from gui.base import BaseWidget
from models import LightingLevel, CaptureMetadata
from core.logging_config import get_logger

logger = get_logger(__name__)


class MetadataPanel(BaseWidget):
    """
    A panel widget for configuring capture metadata.
    
    Features:
    - Lighting level selection
    - Background ID configuration
    - Sequence number management
    - Metadata locking capability
    - Input validation
    """
    
    # Signals
    capture_requested = pyqtSignal()
    metadata_changed = pyqtSignal(CaptureMetadata)
    
    def __init__(self, project_root: str):
        """
        Initialize the metadata panel.
        
        Args:
            project_root: Path to the project root directory
        """
        super().__init__(project_root, "metadata_panel.ui")
    
    def _setup_widget(self) -> None:
        """Setup the metadata panel after UI loading."""
        try:
            # Setup lighting combo box
            if hasattr(self, 'lighting_combo'):
                self.lighting_combo.clear()
                self.lighting_combo.addItems([level.value for level in LightingLevel])
                logger.debug("Lighting combo box configured")
            
            # Set default values
            if hasattr(self, 'background_id_edit'):
                self.background_id_edit.setText("default_bg")
            
            if hasattr(self, 'sequence_number_edit'):
                self.sequence_number_edit.setText("1")
            
            logger.debug("Metadata panel setup completed")
            
        except Exception as e:
            logger.error(f"Error setting up metadata panel: {e}", exc_info=True)
            raise
    
    def _connect_signals(self) -> None:
        """Connect widget signals to slots."""
        try:
            # Connect capture button if it exists
            if hasattr(self, 'capture_button'):
                self.capture_button.clicked.connect(self._on_capture_clicked)
            
            # Connect metadata change signals
            if hasattr(self, 'lighting_combo'):
                self.lighting_combo.currentTextChanged.connect(self._on_metadata_changed)
            
            if hasattr(self, 'background_id_edit'):
                self.background_id_edit.textChanged.connect(self._on_metadata_changed)
            
            if hasattr(self, 'sequence_number_edit'):
                self.sequence_number_edit.textChanged.connect(self._on_metadata_changed)
            
            logger.debug("Metadata panel signals connected")
            
        except Exception as e:
            logger.error(f"Error connecting metadata panel signals: {e}", exc_info=True)
    
    def get_metadata(self) -> CaptureMetadata:
        """
        Get the current metadata from the panel.
        
        Returns:
            CaptureMetadata instance with current values
            
        Raises:
            ValueError: If metadata values are invalid
        """
        try:
            lighting_text = self.lighting_combo.currentText() if hasattr(self, 'lighting_combo') else "normal"
            background_id = self.background_id_edit.text() if hasattr(self, 'background_id_edit') else "default_bg"
            sequence_text = self.sequence_number_edit.text() if hasattr(self, 'sequence_number_edit') else "1"
            
            # Validate sequence number
            try:
                sequence_number = int(sequence_text)
                if sequence_number < 1:
                    raise ValueError("Sequence number must be positive")
            except ValueError as e:
                logger.warning(f"Invalid sequence number '{sequence_text}', using 1")
                sequence_number = 1
            
            metadata = CaptureMetadata(
                lighting=LightingLevel(lighting_text),
                background_id=background_id.strip() or "default_bg",
                sequence_number=sequence_number,
            )
            
            logger.debug(f"Retrieved metadata: {metadata.to_dict()}")
            return metadata
            
        except Exception as e:
            logger.error(f"Error getting metadata: {e}", exc_info=True)
            # Return default metadata on error
            return CaptureMetadata(
                lighting=LightingLevel.NORMAL,
                background_id="default_bg",
                sequence_number=1
            )
    
    def set_metadata(self, metadata: CaptureMetadata) -> None:
        """
        Set the metadata in the panel.
        
        Args:
            metadata: CaptureMetadata instance to set
        """
        try:
            if hasattr(self, 'lighting_combo'):
                self.lighting_combo.setCurrentText(metadata.lighting.value)
            
            if hasattr(self, 'background_id_edit'):
                self.background_id_edit.setText(metadata.background_id)
            
            if hasattr(self, 'sequence_number_edit'):
                self.sequence_number_edit.setText(str(metadata.sequence_number))
            
            logger.debug(f"Set metadata: {metadata.to_dict()}")
            
        except Exception as e:
            logger.error(f"Error setting metadata: {e}", exc_info=True)
    
    def is_locked(self) -> bool:
        """
        Check if metadata is locked.
        
        Returns:
            True if metadata is locked
        """
        if hasattr(self, 'lock_checkbox'):
            return self.lock_checkbox.isChecked()
        return False
    
    def set_locked(self, locked: bool) -> None:
        """
        Set the metadata lock state.
        
        Args:
            locked: Whether to lock the metadata
        """
        if hasattr(self, 'lock_checkbox'):
            self.lock_checkbox.setChecked(locked)
            self._update_widget_states()
    
    def _on_capture_clicked(self) -> None:
        """Handle capture button click."""
        logger.info("Capture button clicked")
        self.capture_requested.emit()
    
    def _on_metadata_changed(self) -> None:
        """Handle metadata changes."""
        try:
            metadata = self.get_metadata()
            self.metadata_changed.emit(metadata)
        except Exception as e:
            logger.error(f"Error handling metadata change: {e}", exc_info=True)
    
    def _update_widget_states(self) -> None:
        """Update widget enabled states based on lock status."""
        locked = self.is_locked()
        
        # Disable/enable widgets based on lock state
        widgets_to_toggle = ['lighting_combo', 'background_id_edit']
        
        for widget_name in widgets_to_toggle:
            if hasattr(self, widget_name):
                widget = getattr(self, widget_name)
                widget.setEnabled(not locked)
    
    @property
    def capture_button(self) -> Optional[QPushButton]:
        """Get the capture button widget."""
        return getattr(self, 'capture_button', None)
    
    @property
    def lock_checkbox(self) -> Optional[QCheckBox]:
        """Get the lock checkbox widget."""
        return getattr(self, 'lock_checkbox', None)
