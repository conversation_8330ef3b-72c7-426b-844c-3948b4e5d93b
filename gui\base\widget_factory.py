"""
Widget factory for creating GUI components.

This module provides a factory pattern for creating widgets
with proper dependency injection and configuration.
"""

from typing import Type, TypeVar, Dict, Any, Optional

from core.logging_config import get_logger
from core.container import get_container

logger = get_logger(__name__)

T = TypeVar('T')


class WidgetFactory:
    """
    Factory for creating GUI widgets with dependency injection.
    
    This factory provides:
    - Centralized widget creation
    - Dependency injection for widgets
    - Configuration passing
    - Error handling during widget creation
    """
    
    def __init__(self, project_root: str):
        """
        Initialize the widget factory.
        
        Args:
            project_root: Path to the project root directory
        """
        self._project_root = project_root
        self._container = get_container()
    
    def create_widget(
        self, 
        widget_class: Type[T], 
        **kwargs: Any
    ) -> T:
        """
        Create a widget instance with dependency injection.
        
        Args:
            widget_class: The widget class to instantiate
            **kwargs: Additional keyword arguments for the widget
            
        Returns:
            Widget instance
            
        Raises:
            Exception: If widget creation fails
        """
        try:
            # Always pass project_root as the first argument
            widget_kwargs = {'project_root': self._project_root}
            widget_kwargs.update(kwargs)
            
            logger.debug(f"Creating widget: {widget_class.__name__}")
            widget = widget_class(**widget_kwargs)
            
            # Inject dependencies if the widget has a setup_dependencies method
            if hasattr(widget, 'setup_dependencies'):
                widget.setup_dependencies(self._container)
            
            logger.debug(f"Successfully created widget: {widget_class.__name__}")
            return widget
            
        except Exception as e:
            logger.error(f"Failed to create widget {widget_class.__name__}: {e}", exc_info=True)
            raise
    
    def create_preview_grid(self, device_manager) -> 'PreviewGrid':
        """
        Create a preview grid widget.
        
        Args:
            device_manager: Device manager instance
            
        Returns:
            PreviewGrid widget
        """
        from gui.widgets.preview_widget import PreviewGrid
        return self.create_widget(PreviewGrid, device_manager=device_manager)
    
    def create_metadata_panel(self) -> 'MetadataPanel':
        """
        Create a metadata panel widget.
        
        Returns:
            MetadataPanel widget
        """
        from gui.widgets.metadata_panel import MetadataPanel
        return self.create_widget(MetadataPanel)
    
    def create_log_panel(self) -> 'LogPanel':
        """
        Create a log panel widget.
        
        Returns:
            LogPanel widget
        """
        from gui.widgets.log_panel import LogPanel
        return self.create_widget(LogPanel)
    
    def create_settings_panel(self, default_path: str) -> 'SettingsPanel':
        """
        Create a settings panel widget.
        
        Args:
            default_path: Default storage path
            
        Returns:
            SettingsPanel widget
        """
        from gui.widgets.settings_panel import SettingsPanel
        return self.create_widget(SettingsPanel, default_path=default_path)
