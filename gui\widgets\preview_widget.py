"""
Preview widget components for camera display.

This module provides widgets for displaying camera previews
with proper threading and frame management.
"""

import time
import threading
from typing import List, Dict, Optional
import math

import cv2
import numpy as np
from PyQt6.QtWidgets import QWidget, QGridLayout, QLabel
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QObject
from PyQt6.QtGui import QImage, QPixmap

from gui.base import BaseWidget
from devices.abstract_camera import AbstractCamera
from models.camera import Frame
from core.logging_config import get_logger

logger = get_logger(__name__)


class FrameWorker(QObject):
    """
    Worker to fetch frames from a camera in a background thread.
    
    This worker runs in a separate thread and continuously fetches
    frames from a camera, emitting signals when new frames are available.
    """
    
    frame_ready = pyqtSignal(object)
    
    def __init__(self, camera: AbstractCamera):
        """
        Initialize the frame worker.
        
        Args:
            camera: Camera instance to capture frames from
        """
        super().__init__()
        self.camera = camera
        self.running = True
        self._lock = threading.Lock()
        self._last_frame: Optional[Frame] = None
        self._frame_count = 0
    
    def run(self) -> None:
        """Continuously fetch frames from the camera."""
        logger.debug(f"Starting frame worker for camera {self.camera.camera_id}")
        
        while self.running:
            try:
                if self.camera.is_connected:
                    frame = self.camera.capture_frame()
                    if frame:
                        with self._lock:
                            self._last_frame = frame
                            self._frame_count += 1
                        
                        self.frame_ready.emit(frame)
                        
                        # Log frame info periodically
                        if self._frame_count % 100 == 0:
                            logger.debug(f"Camera {self.camera.camera_id}: {self._frame_count} frames captured")
                
                time.sleep(0.03)  # ~30 FPS
                
            except Exception as e:
                logger.error(f"Error in FrameWorker for {self.camera.camera_id}: {e}")
                time.sleep(1)  # Wait before retrying
        
        logger.debug(f"Frame worker stopped for camera {self.camera.camera_id}")
    
    def get_last_frame(self) -> Optional[Frame]:
        """
        Get the last captured frame in a thread-safe way.
        
        Returns:
            Last captured frame or None
        """
        with self._lock:
            return self._last_frame
    
    def stop(self) -> None:
        """Stop the frame worker."""
        self.running = False


class PreviewWidget(BaseWidget):
    """
    A widget to display a single camera's preview.
    
    Features:
    - Real-time frame display
    - Automatic image scaling
    - Error handling for invalid frames
    - Camera ID display
    """
    
    def __init__(self, project_root: str, camera_id: str):
        """
        Initialize the preview widget.
        
        Args:
            project_root: Path to the project root directory
            camera_id: ID of the camera to display
        """
        self._camera_id = camera_id
        self._frame_count = 0
        super().__init__(project_root, "preview_widget.ui")
    
    def _setup_widget(self) -> None:
        """Setup the preview widget after UI loading."""
        try:
            # Set camera ID label
            if hasattr(self, 'camera_id_label'):
                self.camera_id_label.setText(self._camera_id)
            
            # Initialize image label
            if hasattr(self, 'image_label'):
                self.image_label.setText("No Frame")
                self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                self.image_label.setStyleSheet("border: 1px solid gray;")
            
            logger.debug(f"Preview widget setup completed for camera {self._camera_id}")
            
        except Exception as e:
            logger.error(f"Error setting up preview widget: {e}", exc_info=True)
            raise
    
    def update_frame(self, frame: Optional[Frame]) -> None:
        """
        Update the displayed frame data.
        
        Args:
            frame: Frame to display or None
        """
        if not hasattr(self, 'image_label'):
            logger.warning("Cannot update frame: image_label not available")
            return
        
        if frame is None:
            self.image_label.setText("No Frame")
            return
        
        try:
            if not isinstance(frame.rgb_image, np.ndarray):
                self.image_label.setText("Invalid Frame Type")
                return
            
            rgb_image = frame.rgb_image
            if rgb_image is None or rgb_image.size == 0:
                self.image_label.setText("Empty Frame")
                return
            
            # Log frame info for first few frames
            self._frame_count += 1
            if self._frame_count <= 3:
                logger.debug(
                    f"Frame info for {self._camera_id} - "
                    f"Shape: {rgb_image.shape}, dtype: {rgb_image.dtype}"
                )
            
            # Convert and scale the image
            display_image = self._prepare_display_image(rgb_image)
            if display_image is not None:
                self.image_label.setPixmap(display_image)
            else:
                self.image_label.setText("Display Error")
            
        except Exception as e:
            logger.error(f"Error updating frame for {self._camera_id}: {e}")
            self.image_label.setText("Display Error")
    
    def _prepare_display_image(self, rgb_image: np.ndarray) -> Optional[QPixmap]:
        """
        Prepare an image for display in the widget.
        
        Args:
            rgb_image: RGB image array
            
        Returns:
            QPixmap ready for display or None on error
        """
        try:
            # Ensure the image is in the correct format
            if len(rgb_image.shape) == 3 and rgb_image.shape[2] == 3:
                # RGB image
                height, width, channel = rgb_image.shape
                bytes_per_line = 3 * width
                
                # Convert to QImage
                q_image = QImage(
                    rgb_image.data, 
                    width, 
                    height, 
                    bytes_per_line, 
                    QImage.Format.Format_RGB888
                )
            else:
                logger.warning(f"Unexpected image shape: {rgb_image.shape}")
                return None
            
            # Convert to QPixmap and scale
            pixmap = QPixmap.fromImage(q_image)
            
            # Scale to fit the widget while maintaining aspect ratio
            if hasattr(self, 'image_label'):
                label_size = self.image_label.size()
                if label_size.width() > 0 and label_size.height() > 0:
                    scaled_pixmap = pixmap.scaled(
                        label_size,
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation
                    )
                    return scaled_pixmap
            
            return pixmap
            
        except Exception as e:
            logger.error(f"Error preparing display image: {e}")
            return None
    
    @property
    def camera_id(self) -> str:
        """Get the camera ID."""
        return self._camera_id


class PreviewGrid(QWidget):
    """
    A grid of preview widgets that uses background threads for updates.

    Features:
    - Automatic grid layout based on camera count
    - Background thread management for each camera
    - Thread-safe frame access
    - Proper cleanup on shutdown
    """

    def __init__(self, project_root: str, device_manager):
        """
        Initialize the preview grid.

        Args:
            project_root: Path to the project root directory
            device_manager: Device manager instance
        """
        super().__init__()
        self.project_root = project_root
        self.device_manager = device_manager
        self.previews: Dict[str, PreviewWidget] = {}
        self.workers: Dict[str, FrameWorker] = {}
        self.threads: List[tuple] = []

        self._setup_grid()
        logger.info(f"Preview grid initialized with {len(self.previews)} cameras")

    def _setup_grid(self) -> None:
        """Setup the grid layout and preview widgets."""
        layout = QGridLayout()
        self.setLayout(layout)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        cameras = self.device_manager.get_all_cameras()
        if not cameras:
            logger.warning("No cameras available for preview grid")
            return

        # Calculate grid dimensions
        num_cameras = len(cameras)
        num_cols = math.ceil(math.sqrt(num_cameras))
        num_rows = math.ceil(num_cameras / num_cols)

        logger.info(f"Setting up {num_rows}x{num_cols} grid for {num_cameras} cameras")

        # Create and position preview widgets
        for i, camera in enumerate(cameras):
            row, col = divmod(i, num_cols)

            # Create preview widget
            preview = PreviewWidget(self.project_root, camera.camera_id)
            self.previews[camera.camera_id] = preview
            layout.addWidget(preview, row, col)

            # Setup worker thread
            self._setup_camera_thread(camera)

        # Configure grid stretching
        for r in range(num_rows):
            layout.setRowStretch(r, 1)
        for c in range(num_cols):
            layout.setColumnStretch(c, 1)

        # Center the grid content
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

    def _setup_camera_thread(self, camera: AbstractCamera) -> None:
        """
        Setup a worker thread for a camera.

        Args:
            camera: Camera to setup thread for
        """
        try:
            thread = QThread()
            worker = FrameWorker(camera)
            self.workers[camera.camera_id] = worker
            worker.moveToThread(thread)

            # Connect signals
            worker.frame_ready.connect(self.on_frame_ready)
            thread.started.connect(worker.run)

            self.threads.append((thread, worker))
            thread.start()

            logger.debug(f"Started worker thread for camera {camera.camera_id}")

        except Exception as e:
            logger.error(f"Error setting up thread for camera {camera.camera_id}: {e}")

    def on_frame_ready(self, frame: Frame) -> None:
        """
        Slot to receive a frame and update the corresponding preview.

        Args:
            frame: Frame to display
        """
        if frame and frame.camera_id in self.previews:
            self.previews[frame.camera_id].update_frame(frame)

    def get_last_frames(self) -> List[Frame]:
        """
        Get the last frame from each worker.

        Returns:
            List of the most recent frames from all cameras
        """
        frames = []
        for worker in self.workers.values():
            frame = worker.get_last_frame()
            if frame:
                frames.append(frame)

        logger.debug(f"Retrieved {len(frames)} frames from {len(self.workers)} workers")
        return frames

    def stop_threads(self) -> None:
        """Stop all background threads."""
        logger.info("Stopping all preview threads...")

        for thread, worker in self.threads:
            try:
                worker.stop()
                thread.quit()
                if not thread.wait(5000):  # 5 second timeout
                    logger.warning(f"Thread did not stop gracefully, terminating...")
                    thread.terminate()
                    thread.wait()
            except Exception as e:
                logger.error(f"Error stopping thread: {e}")

        self.threads.clear()
        self.workers.clear()
        logger.info("All preview threads stopped")
