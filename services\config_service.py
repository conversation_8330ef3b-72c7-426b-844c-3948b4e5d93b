"""
Configuration service for accessing application settings.

This service provides a convenient interface to access configuration
settings throughout the application using the centralized config system.
"""

from typing import Dict, Any, Tuple

from core.config import get_config, ApplicationConfig
from core.logging_config import get_logger

logger = get_logger(__name__)


class ConfigService:
    """
    Service to provide access to application configuration.

    This service acts as a facade over the centralized configuration
    system, providing convenient methods for accessing common settings.
    """

    def __init__(self):
        """Initialize the config service."""
        self._config: ApplicationConfig = get_config()

    @property
    def camera_resolution(self) -> Tuple[int, int]:
        """
        Get the camera resolution as a (width, height) tuple.

        Returns:
            Tuple of (width, height) in pixels
        """
        res_str = self._config.camera.resolution
        try:
            width, height = map(int, res_str.split('x'))
            return width, height
        except ValueError:
            logger.warning(f"Invalid camera_resolution format '{res_str}'. Using 640x480.")
            return 640, 480

    @property
    def camera_fps(self) -> int:
        """
        Get the camera frames per second.

        Returns:
            FPS value
        """
        return self._config.camera.fps

    @property
    def camera_exposure_settings(self) -> Dict[str, int]:
        """
        Get camera exposure settings.

        Returns:
            Dictionary mapping camera types to exposure values
        """
        return self._config.camera.exposure.copy()

    @property
    def zed_settings(self) -> Dict[str, Any]:
        """
        Get ZED-specific settings.

        Returns:
            Dictionary of ZED configuration settings
        """
        return {
            'enable_self_calibration': self._config.zed.enable_self_calibration,
            'depth_mode': self._config.zed.depth_mode,
            'depth_stabilization': self._config.zed.depth_stabilization,
            'depth_minimum_distance': self._config.zed.depth_minimum_distance
        }

    @property
    def storage_settings(self) -> Dict[str, str]:
        """
        Get storage configuration settings.

        Returns:
            Dictionary of storage settings
        """
        return {
            'dataset_root_dir': self._config.storage.dataset_root_dir,
            'directory_format': self._config.storage.directory_format
        }

    def get_full_config(self) -> ApplicationConfig:
        """
        Get the complete application configuration.

        Returns:
            ApplicationConfig instance
        """
        return self._config

