<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>PreviewWidget</class>
 <widget class="QWidget" name="PreviewWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>300</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>300</width>
    <height>200</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>400</width>
    <height>300</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>5</number>
   </property>
   <property name="leftMargin">
    <number>5</number>
   </property>
   <property name="topMargin">
    <number>5</number>
   </property>
   <property name="rightMargin">
    <number>5</number>
   </property>
   <property name="bottomMargin">
    <number>5</number>
   </property>
   <item>
    <widget class="QLabel" name="camera_id_label">
     <property name="styleSheet">
      <string notr="true">font-weight: bold; padding: 2px;</string>
     </property>
     <property name="text">
      <string>Camera ID</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="image_label">
     <property name="minimumSize">
      <size>
       <width>280</width>
       <height>160</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">border: 1px solid #ccc; background-color: #f0f0f0;</string>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
