"""
Settings panel widget for data saving configuration.

This module provides a widget for configuring data saving settings
including file formats, storage paths, and export options.
"""

from typing import Dict, Any, Optional
from pathlib import Path

from PyQt6.QtWidgets import QFileDialog, QPushButton, QLineEdit, QCheckBox
from PyQt6.QtCore import pyqtSignal

from gui.base import BaseWidget
from core.logging_config import get_logger

logger = get_logger(__name__)


class SettingsPanel(BaseWidget):
    """
    A panel widget for configuring data saving settings.
    
    Features:
    - Storage path selection
    - File format options (RGB, Depth, Point Cloud)
    - Path validation
    - Settings persistence
    """
    
    # Signals
    settings_changed = pyqtSignal(dict)
    path_changed = pyqtSignal(str)
    
    def __init__(self, project_root: str, default_path: str = ""):
        """
        Initialize the settings panel.
        
        Args:
            project_root: Path to the project root directory
            default_path: Default storage path
        """
        self._default_path = default_path or str(Path.home() / "MultiCamDataset")
        super().__init__(project_root, "settings_panel.ui")
    
    def _setup_widget(self) -> None:
        """Setup the settings panel after UI loading."""
        try:
            # Set default path
            if hasattr(self, 'path_edit'):
                self.path_edit.setText(self._default_path)
                logger.debug(f"Set default path: {self._default_path}")
            
            # Set default checkbox states
            default_settings = {
                'save_rgb_checkbox': True,
                'save_depth_checkbox': True,
                'save_point_cloud_checkbox': False
            }
            
            for checkbox_name, default_value in default_settings.items():
                if hasattr(self, checkbox_name):
                    checkbox = getattr(self, checkbox_name)
                    checkbox.setChecked(default_value)
            
            logger.debug("Settings panel setup completed")
            
        except Exception as e:
            logger.error(f"Error setting up settings panel: {e}", exc_info=True)
            raise
    
    def _connect_signals(self) -> None:
        """Connect widget signals to slots."""
        try:
            # Connect browse button
            if hasattr(self, 'browse_button'):
                self.browse_button.clicked.connect(self._browse_for_directory)
            
            # Connect path edit changes
            if hasattr(self, 'path_edit'):
                self.path_edit.textChanged.connect(self._on_path_changed)
            
            # Connect checkbox changes
            checkbox_names = ['save_rgb_checkbox', 'save_depth_checkbox', 'save_point_cloud_checkbox']
            for checkbox_name in checkbox_names:
                if hasattr(self, checkbox_name):
                    checkbox = getattr(self, checkbox_name)
                    checkbox.stateChanged.connect(self._on_settings_changed)
            
            logger.debug("Settings panel signals connected")
            
        except Exception as e:
            logger.error(f"Error connecting settings panel signals: {e}", exc_info=True)
    
    def _browse_for_directory(self) -> None:
        """Open a dialog to select a storage directory."""
        try:
            current_path = self.path_edit.text() if hasattr(self, 'path_edit') else self._default_path
            
            directory = QFileDialog.getExistingDirectory(
                self, 
                "Select Storage Directory", 
                current_path
            )
            
            if directory:
                if hasattr(self, 'path_edit'):
                    self.path_edit.setText(directory)
                logger.info(f"Selected storage directory: {directory}")
            
        except Exception as e:
            logger.error(f"Error browsing for directory: {e}", exc_info=True)
    
    def get_settings(self) -> Dict[str, Any]:
        """
        Get the current settings from the panel.
        
        Returns:
            Dictionary containing current settings
        """
        try:
            settings = {
                "save_rgb": self._get_checkbox_state('save_rgb_checkbox', True),
                "save_depth": self._get_checkbox_state('save_depth_checkbox', True),
                "save_point_cloud": self._get_checkbox_state('save_point_cloud_checkbox', False),
                "path": self.path_edit.text() if hasattr(self, 'path_edit') else self._default_path,
            }
            
            logger.debug(f"Retrieved settings: {settings}")
            return settings
            
        except Exception as e:
            logger.error(f"Error getting settings: {e}", exc_info=True)
            # Return default settings on error
            return {
                "save_rgb": True,
                "save_depth": True,
                "save_point_cloud": False,
                "path": self._default_path,
            }
    
    def set_settings(self, settings: Dict[str, Any]) -> None:
        """
        Set the settings in the panel.
        
        Args:
            settings: Dictionary containing settings to set
        """
        try:
            # Set checkboxes
            checkbox_mapping = {
                'save_rgb_checkbox': settings.get('save_rgb', True),
                'save_depth_checkbox': settings.get('save_depth', True),
                'save_point_cloud_checkbox': settings.get('save_point_cloud', False)
            }
            
            for checkbox_name, value in checkbox_mapping.items():
                if hasattr(self, checkbox_name):
                    checkbox = getattr(self, checkbox_name)
                    checkbox.setChecked(value)
            
            # Set path
            if hasattr(self, 'path_edit'):
                path = settings.get('path', self._default_path)
                self.path_edit.setText(path)
            
            logger.debug(f"Set settings: {settings}")
            
        except Exception as e:
            logger.error(f"Error setting settings: {e}", exc_info=True)
    
    def validate_path(self, path: str) -> bool:
        """
        Validate the storage path.
        
        Args:
            path: Path to validate
            
        Returns:
            True if path is valid
        """
        try:
            path_obj = Path(path)
            
            # Check if path exists or can be created
            if path_obj.exists():
                return path_obj.is_dir()
            else:
                # Try to create the directory
                path_obj.mkdir(parents=True, exist_ok=True)
                return True
                
        except Exception as e:
            logger.warning(f"Invalid storage path '{path}': {e}")
            return False
    
    def _get_checkbox_state(self, checkbox_name: str, default: bool = False) -> bool:
        """Get the state of a checkbox widget."""
        if hasattr(self, checkbox_name):
            checkbox = getattr(self, checkbox_name)
            return checkbox.isChecked()
        return default
    
    def _on_path_changed(self, path: str) -> None:
        """Handle path changes."""
        logger.debug(f"Storage path changed: {path}")
        self.path_changed.emit(path)
        self._on_settings_changed()
    
    def _on_settings_changed(self) -> None:
        """Handle settings changes."""
        try:
            settings = self.get_settings()
            self.settings_changed.emit(settings)
            logger.debug("Settings changed signal emitted")
        except Exception as e:
            logger.error(f"Error handling settings change: {e}", exc_info=True)
    
    @property
    def storage_path(self) -> str:
        """Get the current storage path."""
        if hasattr(self, 'path_edit'):
            return self.path_edit.text()
        return self._default_path
    
    @property
    def path_edit(self) -> Optional[QLineEdit]:
        """Get the path edit widget."""
        return getattr(self, 'path_edit', None)
    
    @property
    def browse_button(self) -> Optional[QPushButton]:
        """Get the browse button widget."""
        return getattr(self, 'browse_button', None)
