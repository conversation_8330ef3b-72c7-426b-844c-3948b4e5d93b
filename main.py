import sys
import logging
from PyQt6.QtWidgets import QApplication

from core.application import MultiCamApplication
from core.logging_config import setup_logging


# -----------------------------------------------------------------------------
# Application Entry Point
# -----------------------------------------------------------------------------

def main() -> None:
    """Main application entry point."""
    # Setup logging first
    setup_logging()

    # Create and run the application
    qt_app = QApplication(sys.argv)
    multicam_app = MultiCamApplication()

    try:
        logging.info("MultiCamCollector application starting...")
        multicam_app.initialize()
        multicam_app.show()

        exit_code = qt_app.exec()
        logging.info(f"Application exiting with code {exit_code}")
        sys.exit(exit_code)

    except Exception as e:
        logging.error(f"Fatal error during application startup: {e}", exc_info=True)
        sys.exit(1)
    finally:
        multicam_app.cleanup()


if __name__ == "__main__":
    main()
