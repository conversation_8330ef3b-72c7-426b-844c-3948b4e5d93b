<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1800</width>
    <height>1000</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Multi-Camera Collector</string>
  </property>
  <widget class="QWidget" name="central_widget">
   <layout class="QHBoxLayout" name="root_layout">
    <item>
     <widget class="QSplitter" name="main_splitter">
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
      <widget class="QSplitter" name="left_splitter">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
      </widget>
      <widget class="QWidget" name="right_widget">
       <layout class="QVBoxLayout" name="right_layout"/>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
