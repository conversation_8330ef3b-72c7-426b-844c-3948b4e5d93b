"""
Base widget class for all GUI components.

This module provides a common base class for all custom widgets
with standardized initialization and error handling.
"""

import os
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Optional

from PyQt6 import uic
from PyQt6.QtWidgets import QWidget

from core.logging_config import get_logger

logger = get_logger(__name__)


class BaseWidget(QWidget, ABC):
    """
    Base class for all custom widgets in the application.
    
    This class provides:
    - Standardized UI file loading
    - Error handling for UI operations
    - Logging integration
    - Common widget lifecycle methods
    """
    
    def __init__(self, project_root: str, ui_filename: Optional[str] = None):
        """
        Initialize the base widget.
        
        Args:
            project_root: Path to the project root directory
            ui_filename: Name of the UI file (if None, derived from class name)
        """
        super().__init__()
        self._project_root = project_root
        self._ui_filename = ui_filename or self._get_default_ui_filename()
        self._initialized = False
        
        try:
            self._load_ui()
            self._setup_widget()
            self._connect_signals()
            self._initialized = True
            logger.debug(f"{self.__class__.__name__} initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize {self.__class__.__name__}: {e}", exc_info=True)
            raise
    
    def _get_default_ui_filename(self) -> str:
        """
        Get the default UI filename based on the class name.
        
        Returns:
            UI filename (e.g., 'log_panel.ui' for LogPanel class)
        """
        class_name = self.__class__.__name__
        # Convert CamelCase to snake_case
        ui_name = ''.join(['_' + c.lower() if c.isupper() and i > 0 
                          else c.lower() for i, c in enumerate(class_name)])
        return f"{ui_name}.ui"
    
    def _load_ui(self) -> None:
        """
        Load the UI file for this widget.
        
        Raises:
            FileNotFoundError: If UI file doesn't exist
            Exception: If UI loading fails
        """
        ui_path = Path(self._project_root) / "ui" / self._ui_filename
        
        if not ui_path.exists():
            raise FileNotFoundError(f"UI file not found: {ui_path}")
        
        try:
            uic.loadUi(str(ui_path), self)
            logger.debug(f"Loaded UI file: {ui_path}")
        except Exception as e:
            logger.error(f"Failed to load UI file {ui_path}: {e}")
            raise
    
    @abstractmethod
    def _setup_widget(self) -> None:
        """
        Setup the widget after UI loading.
        
        This method should be implemented by subclasses to perform
        any additional setup after the UI is loaded.
        """
        pass
    
    def _connect_signals(self) -> None:
        """
        Connect widget signals to slots.
        
        This method can be overridden by subclasses to connect
        signals and slots. Default implementation does nothing.
        """
        pass
    
    @property
    def is_initialized(self) -> bool:
        """Check if the widget is properly initialized."""
        return self._initialized
    
    @property
    def project_root(self) -> str:
        """Get the project root directory."""
        return self._project_root
