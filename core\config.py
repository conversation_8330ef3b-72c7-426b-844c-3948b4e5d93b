"""
Configuration management for the MultiCamCollector application.

This module provides centralized configuration loading and validation
using dataclasses and type hints for better maintainability.
"""

import os
import yaml
from dataclasses import dataclass, field
from typing import Dict, Any, Optional
from pathlib import Path

from core.logging_config import get_logger

logger = get_logger(__name__)


@dataclass
class CameraConfig:
    """Camera-specific configuration settings."""
    resolution: str = "1280x720"
    fps: int = 30
    exposure: Dict[str, int] = field(default_factory=lambda: {"d435i": 150, "zed2i": 100})


@dataclass
class ZedConfig:
    """ZED camera specific configuration."""
    enable_self_calibration: bool = False
    depth_mode: str = "PERFORMANCE"
    depth_stabilization: bool = True
    depth_minimum_distance: int = 200


@dataclass
class StorageConfig:
    """Storage and dataset configuration."""
    dataset_root_dir: str = "../the-dataset"
    directory_format: str = "{date}/{lighting}/{background_id}"


@dataclass
class LoggingConfig:
    """Logging configuration."""
    log_level: str = "INFO"
    log_file: str = "multicam_collector.log"
    log_dir: str = "logs"
    enable_console: bool = True
    enable_file: bool = True
    redirect_stdout: bool = True


@dataclass
class ApplicationConfig:
    """Main application configuration."""
    camera: CameraConfig = field(default_factory=CameraConfig)
    zed: ZedConfig = field(default_factory=ZedConfig)
    storage: StorageConfig = field(default_factory=StorageConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    
    @classmethod
    def load_from_file(cls, config_path: str) -> 'ApplicationConfig':
        """
        Load configuration from a YAML file.
        
        Args:
            config_path: Path to the configuration file
            
        Returns:
            ApplicationConfig instance
            
        Raises:
            FileNotFoundError: If config file doesn't exist
            yaml.YAMLError: If config file is invalid YAML
        """
        config_file = Path(config_path)
        
        if not config_file.exists():
            logger.warning(f"Config file {config_path} not found, using defaults")
            return cls()
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f) or {}
            
            logger.info(f"Loaded configuration from {config_path}")
            return cls._from_dict(config_data)
            
        except yaml.YAMLError as e:
            logger.error(f"Error parsing config file {config_path}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error loading config: {e}")
            raise
    
    @classmethod
    def _from_dict(cls, data: Dict[str, Any]) -> 'ApplicationConfig':
        """Create ApplicationConfig from dictionary data."""
        camera_data = data.get('camera', {})
        if isinstance(camera_data, dict):
            # Handle the flat structure from the original config.yaml
            camera_config = CameraConfig(
                resolution=camera_data.get('camera_resolution', "1280x720"),
                fps=camera_data.get('camera_fps', 30),
                exposure=camera_data.get('exposure', {"d435i": 150, "zed2i": 100})
            )
        else:
            camera_config = CameraConfig()
        
        zed_config = ZedConfig(**data.get('zed', {}))
        
        storage_data = data.get('storage', {})
        storage_config = StorageConfig(
            dataset_root_dir=storage_data.get('dataset_root_dir', "../the-dataset"),
            directory_format=storage_data.get('directory_format', "{date}/{lighting}/{background_id}")
        )
        
        logging_data = data.get('logging', {})
        logging_config = LoggingConfig(
            log_level=logging_data.get('log_level', "INFO"),
            log_file=logging_data.get('log_file', "multicam_collector.log")
        )
        
        return cls(
            camera=camera_config,
            zed=zed_config,
            storage=storage_config,
            logging=logging_config
        )
    
    def save_to_file(self, config_path: str) -> None:
        """
        Save configuration to a YAML file.
        
        Args:
            config_path: Path where to save the configuration
        """
        config_data = {
            'camera_resolution': self.camera.resolution,
            'camera_fps': self.camera.fps,
            'exposure': self.camera.exposure,
            'zed': {
                'enable_self_calibration': self.zed.enable_self_calibration,
                'depth_mode': self.zed.depth_mode,
                'depth_stabilization': self.zed.depth_stabilization,
                'depth_minimum_distance': self.zed.depth_minimum_distance
            },
            'dataset_root_dir': self.storage.dataset_root_dir,
            'directory_format': self.storage.directory_format,
            'log_level': self.logging.log_level,
            'log_file': self.logging.log_file
        }
        
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f, default_flow_style=False, indent=2)
            logger.info(f"Configuration saved to {config_path}")
        except Exception as e:
            logger.error(f"Error saving config to {config_path}: {e}")
            raise


# Global configuration instance
_config: Optional[ApplicationConfig] = None


def get_config() -> ApplicationConfig:
    """
    Get the global application configuration.
    
    Returns:
        ApplicationConfig instance
        
    Raises:
        RuntimeError: If configuration hasn't been initialized
    """
    global _config
    if _config is None:
        raise RuntimeError("Configuration not initialized. Call load_config() first.")
    return _config


def load_config(config_path: str = "config.yaml") -> ApplicationConfig:
    """
    Load and initialize the global application configuration.
    
    Args:
        config_path: Path to the configuration file
        
    Returns:
        ApplicationConfig instance
    """
    global _config
    _config = ApplicationConfig.load_from_file(config_path)
    return _config


def is_config_loaded() -> bool:
    """Check if configuration has been loaded."""
    return _config is not None
