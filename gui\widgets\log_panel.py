"""
Log panel widget for displaying application messages.

This module provides a widget for displaying log messages
with proper formatting and scrolling capabilities.
"""

from typing import Op<PERSON>
from datetime import datetime

from PyQt6.QtCore import QTimer
from PyQt6.QtWidgets import QTextEdit
from PyQt6.QtGui import QTextCursor

from gui.base import BaseWidget
from core.logging_config import get_logger

logger = get_logger(__name__)


class LogPanel(BaseWidget):
    """
    A panel widget for displaying log messages.
    
    Features:
    - Automatic scrolling to latest messages
    - Message timestamping
    - Maximum message limit to prevent memory issues
    - Color coding for different message types
    """
    
    MAX_MESSAGES = 1000  # Maximum number of messages to keep
    
    def __init__(self, project_root: str):
        """
        Initialize the log panel.
        
        Args:
            project_root: Path to the project root directory
        """
        self._message_count = 0
        super().__init__(project_root, "log_panel.ui")
    
    def _setup_widget(self) -> None:
        """Setup the log panel after UI loading."""
        # Configure the text edit widget
        if hasattr(self, 'log_text_edit'):
            self.log_text_edit.setReadOnly(True)
            self.log_text_edit.setMaximumBlockCount(self.MAX_MESSAGES)
            
            # Set up auto-scroll timer to prevent excessive scrolling
            self._scroll_timer = QTimer()
            self._scroll_timer.setSingleShot(True)
            self._scroll_timer.timeout.connect(self._scroll_to_bottom)
            
            logger.debug("Log panel setup completed")
        else:
            logger.warning("log_text_edit widget not found in UI file")
    
    def add_log_message(self, message: str, level: str = "INFO") -> None:
        """
        Add a message to the log panel.
        
        Args:
            message: The message to add
            level: Log level (INFO, WARNING, ERROR, etc.)
        """
        if not hasattr(self, 'log_text_edit'):
            logger.warning("Cannot add log message: log_text_edit not available")
            return
        
        try:
            # Format the message with timestamp
            timestamp = datetime.now().strftime("%H:%M:%S")
            formatted_message = f"[{timestamp}] {level}: {message}"
            
            # Add the message
            self.log_text_edit.append(formatted_message)
            self._message_count += 1
            
            # Schedule auto-scroll (debounced)
            self._scroll_timer.start(100)  # 100ms delay
            
            logger.debug(f"Added log message: {message[:50]}...")
            
        except Exception as e:
            logger.error(f"Error adding log message: {e}", exc_info=True)
    
    def add_info_message(self, message: str) -> None:
        """Add an info-level message."""
        self.add_log_message(message, "INFO")
    
    def add_warning_message(self, message: str) -> None:
        """Add a warning-level message."""
        self.add_log_message(message, "WARNING")
    
    def add_error_message(self, message: str) -> None:
        """Add an error-level message."""
        self.add_log_message(message, "ERROR")
    
    def clear_messages(self) -> None:
        """Clear all messages from the log panel."""
        if hasattr(self, 'log_text_edit'):
            self.log_text_edit.clear()
            self._message_count = 0
            logger.debug("Log panel cleared")
    
    def _scroll_to_bottom(self) -> None:
        """Scroll the log panel to the bottom."""
        if hasattr(self, 'log_text_edit'):
            scrollbar = self.log_text_edit.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())
    
    @property
    def message_count(self) -> int:
        """Get the current number of messages."""
        return self._message_count
