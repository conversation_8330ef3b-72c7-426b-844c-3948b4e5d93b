"""
GUI package for MultiCamCollector.

This package contains all GUI components organized into logical modules:
- Base classes and utilities
- Widget components
- Widget factory for dependency injection
"""

# Import from new widget structure
from .widgets import (
    PreviewWidget,
    PreviewGrid,
    MetadataPanel,
    LogPanel,
    SettingsPanel
)

# Import base classes and factory
from .base import BaseWidget, WidgetFactory

__all__ = [
    'PreviewWidget',
    'PreviewGrid',
    'MetadataPanel',
    'LogPanel',
    'SettingsPanel',
    'BaseWidget',
    'WidgetFactory'
]
